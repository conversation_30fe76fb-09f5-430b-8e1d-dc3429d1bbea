import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import FullScreenToggle from "./utils/full-screen-toggle";
import { LanguageToggle } from "./utils/language-toggle";
import { ModeToggle } from "./utils/mode-toggle";

export default function Navbar() {
  // const { pathname } = useLocation();

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />
        {/* <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink asChild>
                <Link to={URLS.home}>GPS Tracking</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {pathname !== "/" && (
              <BreadcrumbSeparator className="hidden md:block" />
            )}
            <BreadcrumbItem>
              <BreadcrumbPage>
                {pathname.split("/")[1].charAt(0).toUpperCase() +
                  pathname.split("/")[1].slice(1)}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb> */}
      </div>

      <div className="ms-auto me-4 flex gap-2">
        <LanguageToggle />
        <FullScreenToggle />
        <ModeToggle />
      </div>
    </header>
  );
}
