import AddAreaForm from "@/components/areas/add-area-form";
import { AreaMap } from "@/components/areas/area-map";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState } from "react";

export default function AddAreaPage() {
  const isMobile = useIsMobile();

  const [tableSize, setTableSize] = useState(
    localStorage.getItem("gps-table-size")
      ? JSON.parse(localStorage.getItem("gps-table-size")!)
      : 50,
  );

  const handleResize = (e: number) => {
    setTableSize(e);
    localStorage.setItem("gps-table-size", JSON.stringify(e));
  };

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <AddAreaForm />
        </div>
        <div className="h-[500px] w-full p-2">
          <AreaMap />
        </div>
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={30}
      >
        <AddAreaForm />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">
        <AreaMap />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}
