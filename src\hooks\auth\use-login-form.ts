import { authAtom } from "@/atoms/auth/auth-atom";
import type { LoginFormData } from "@/types/auth";
import { URLS } from "@/utils/urls";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router";
import { z } from "zod";

const FormSchema = z.object({
  email: z.email("Invalid email"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

export default function useLoginForm() {
  const navigate = useNavigate();

  const { loading } = authAtom.useValue();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "<EMAIL>",
      password: "12345678",
    },
  });

  function onSubmit(data: LoginFormData) {
    authAtom.login(data, () => {
      navigate(URLS.home);
    });
  }

  return {
    form,
    onSubmit,
    loading,
  };
}
