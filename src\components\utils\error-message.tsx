import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { TriangleAlert } from "lucide-react";

interface ErrorMessageProps {
  title?: string;
  message: string;
}

export function ErrorMessage({ title = "Error", message }: ErrorMessageProps) {
  return (
    <Alert
      variant="destructive"
      className="border-destructive/30 bg-destructive/10 flex items-start gap-3 rounded-2xl border p-4 shadow-sm"
    >
      <TriangleAlert className="text-destructive mt-0.5 h-5 w-5" />
      <div className="flex flex-col">
        <AlertTitle className="text-destructive text-base font-semibold">
          {title}
        </AlertTitle>
        <AlertDescription className="text-muted-foreground text-sm">
          {message}
        </AlertDescription>
      </div>
    </Alert>
  );
}
