import { But<PERSON> } from "@/components/ui/button";
import { URLS } from "@/utils/urls";
import { Link } from "react-router";

export default function NotFoundPage() {
  return (
    <div className="bg-background text-foreground flex min-h-screen flex-col items-center justify-center px-4">
      <h1 className="text-primary text-9xl font-bold">404</h1>
      <h2 className="mt-4 text-2xl font-semibold">Page Not Found</h2>
      <p className="text-muted-foreground mt-2 max-w-md text-center">
        The page you’re looking for doesn’t exist or has been moved.
      </p>

      <Button className="mt-8">
        <Link to={URLS.home}>Go Home</Link>
      </Button>
    </div>
  );
}
