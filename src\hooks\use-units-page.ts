import { useState } from "react";
import { useIsMobile } from "./use-mobile";

export default function useUnitsPage() {
  const isMobile = useIsMobile();

  const [tableSize, setTableSize] = useState(
    localStorage.getItem("gps-table-size")
      ? JSON.parse(localStorage.getItem("gps-table-size")!)
      : 50,
  );

  const handleResize = (e: number) => {
    setTableSize(e);
    localStorage.setItem("gps-table-size", JSON.stringify(e));
  };

  return {
    isMobile,
    tableSize,
    handleResize,
  };
}
