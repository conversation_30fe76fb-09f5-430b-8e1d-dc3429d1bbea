import { But<PERSON> } from "@/components/ui/button";
import {
  Too<PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Facebook, Github, Instagram, Linkedin, Twitter } from "lucide-react";
import { Link } from "react-router";

export function FloatingSidebar() {
  const socials = [
    { name: "GitHub", icon: Github, href: "#" },
    {
      name: "LinkedIn",
      icon: Linkedin,
      href: "#",
    },
    {
      name: "Twitter",
      icon: Twitter,
      href: "#",
    },
    {
      name: "Facebook",
      icon: Facebook,
      href: "#",
    },
    {
      name: "Instagram",
      icon: Instagram,
      href: "#",
    },
  ];

  return (
    <TooltipProvider>
      <aside
        className={cn(
          "fixed top-1/2 right-4 flex -translate-y-1/2 flex-col gap-3 p-3",
          "bg-sidebar border-sidebar-border rounded-2xl border shadow-lg",
          "z-50",
        )}
      >
        {socials.map(({ name, icon: Icon, href }) => (
          <Tooltip key={name}>
            <TooltipTrigger asChild>
              <Link
                to={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sidebar-foreground hover:text-primary transition-colors"
              >
                <Button
                  size="icon"
                  variant="ghost"
                  className="hover:bg-sidebar-accent rounded-full"
                >
                  <Icon className="h-5 w-5" />
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="right">{name}</TooltipContent>
          </Tooltip>
        ))}
      </aside>
    </TooltipProvider>
  );
}
