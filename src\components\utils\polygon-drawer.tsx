import { LatLng } from "leaflet";
import { useRef, useState } from "react";
import { Polygon, useMap, useMapEvents } from "react-leaflet";

type PolygonDrawerProps = {
  color?: string;
  onPolygonComplete?: (points: { lat: number; lng: number }[]) => void;
  allowMultiple?: boolean;
};

export function PolygonDrawer({
  color = "green",
  onPolygonComplete,
  allowMultiple = false,
}: PolygonDrawerProps) {
  const [polygons, setPolygons] = useState<LatLng[][]>([]);
  const [currentPolygon, setCurrentPolygon] = useState<LatLng[]>([]);
  const [previewPoint, setPreviewPoint] = useState<LatLng | null>(null);

  const isDrawing = useRef(false);
  const map = useMap();

  useMapEvents({
    click(e) {
      if (!isDrawing.current) {
        isDrawing.current = true;
        map.doubleClickZoom.disable();
        setCurrentPolygon([e.latlng]);
      } else {
        setCurrentPolygon((prev) => [...prev, e.latlng]);
      }
      setPreviewPoint(null);
    },
    mousemove(e) {
      if (isDrawing.current && currentPolygon.length > 0) {
        setPreviewPoint(e.latlng);
      }
    },
    dblclick() {
      if (isDrawing.current && currentPolygon.length > 2) {
        let finished = [...currentPolygon];

        // close polygon if not closed
        const first = finished[0];
        const last = finished[finished.length - 1];
        if (first.lat !== last.lat || first.lng !== last.lng) {
          finished = [...finished, first];
        }

        setPolygons((prev) =>
          allowMultiple ? [...prev, finished] : [finished],
        );

        // convert to {lat, lng} plain objects
        const plainPoints = finished.map((p) => ({
          lat: p.lat,
          lng: p.lng,
        }));

        onPolygonComplete?.(plainPoints);

        setCurrentPolygon([]);
        setPreviewPoint(null);
        isDrawing.current = false;
        map.doubleClickZoom.enable();
      }
    },
  });

  const previewPolygon =
    isDrawing.current && currentPolygon.length > 0
      ? [...currentPolygon, ...(previewPoint ? [previewPoint] : [])]
      : null;

  return (
    <>
      {polygons.map((p, i) => (
        <Polygon key={i} positions={p} pathOptions={{ color }} />
      ))}
      {previewPolygon && (
        <Polygon
          positions={previewPolygon}
          pathOptions={{ color, dashArray: "4" }}
        />
      )}
    </>
  );
}
