import type {
  LoginFormData,
  LoginResponse,
  LogoutResponse,
  User,
} from "@/types/auth";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

type AuthAtom = {
  /**
   * user
   */
  user: User | null;
  /**
   * token
   */
  token: string;
  /**
   * loading
   */
  loading: boolean;
};

type AuthAtomActions = {
  /**
   * login
   */
  login: (formData: LoginFormData, callback: () => void) => void;
  /**
   * logout
   */
  logout: (callback: () => void) => void;
};

export const authAtom = atom<AuthAtom, AuthAtomActions>({
  key: "authAtom",

  default: {
    /**
     * user
     */
    user: localStorage.getItem("gps-user")
      ? JSON.parse(localStorage.getItem("gps-user")!)
      : null,

    /**
     * token
     */
    token: localStorage.getItem("gps-token")
      ? localStorage.getItem("gps-token")!
      : "",

    /**
     * loading
     */
    loading: false,
  },

  beforeUpdate(newValue) {
    /***
     * save to local storage
     */
    localStorage.setItem("gps-user", JSON.stringify(newValue.user));
    localStorage.setItem("gps-token", newValue.token);

    return newValue;
  },

  actions: {
    /***
     * login
     */
    login: async (formData: LoginFormData, callback: () => void) => {
      try {
        authAtom.change("loading", true);

        const { data } = await endpoint.post<LoginResponse>("/login", formData);

        authAtom.change("user", data.data.user);
        authAtom.change("token", data.data.token);

        callback();

        toast.success(data.message);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      } finally {
        authAtom.change("loading", false);
      }
    },

    /***
     * logout
     */
    logout: async (callback: () => void) => {
      try {
        authAtom.change("loading", true);

        const { data } = await endpoint.post<LogoutResponse>("/logout");

        authAtom.change("user", null);
        authAtom.change("token", "");

        callback();

        toast.success(data.message);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      } finally {
        authAtom.change("loading", false);
      }
    },
  },
});
