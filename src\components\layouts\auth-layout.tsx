import { URLS } from "@/utils/urls";
import { Link, Outlet } from "react-router";
import { FloatingSidebar } from "../auth/floating-sidebar";

export default function AuthLayout() {
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <FloatingSidebar />

      <div className="w-full max-w-sm md:max-w-3xl">
        <Outlet />
      </div>

      <div className="text-muted-foreground *:[a]:hover:text-primary mt-5 text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our{" "}
        <Link to={URLS.termsOfService}>Terms of Service</Link> and{" "}
        <Link to={URLS.privacyPolicy}>Privacy Policy</Link>.
      </div>
    </div>
  );
}
