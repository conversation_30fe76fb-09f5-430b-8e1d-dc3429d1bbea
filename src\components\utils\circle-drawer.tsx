import { LatLng } from "leaflet";
import { useRef, useState } from "react";
import { Circle, useMap, useMapEvents } from "react-leaflet";

type CircleDrawerProps = {
  color?: string;
  onCircleComplete?: (circle: {
    center: { lat: number; lng: number };
    radius: number;
  }) => void;
  allowMultiple?: boolean;
};

export function CircleDrawer({
  color = "blue",
  onCircleComplete,
  allowMultiple = false,
}: CircleDrawerProps) {
  const [circles, setCircles] = useState<{ center: LatLng; radius: number }[]>(
    [],
  );
  const [currentCircle, setCurrentCircle] = useState<{
    center: LatLng;
    radius: number;
  } | null>(null);

  const isDrawing = useRef(false);
  const map = useMap();

  useMapEvents({
    mousedown(e) {
      isDrawing.current = true;
      map.dragging.disable();
      setCurrentCircle({ center: e.latlng, radius: 0 });
    },
    mousemove(e) {
      if (isDrawing.current && currentCircle) {
        const radius = e.latlng.distanceTo(currentCircle.center);
        setCurrentCircle({ ...currentCircle, radius });
      }
    },
    mouseup() {
      if (isDrawing.current && currentCircle) {
        const finished = { ...currentCircle };
        setCircles((prev) =>
          allowMultiple ? [...prev, finished] : [finished],
        );

        // return plain object instead of LatLng
        onCircleComplete?.({
          center: { lat: finished.center.lat, lng: finished.center.lng },
          radius: finished.radius,
        });

        setCurrentCircle(null);
      }
      isDrawing.current = false;
      map.dragging.enable();
    },
  });

  return (
    <>
      {circles.map((c, i) => (
        <Circle
          key={i}
          center={c.center}
          radius={c.radius}
          pathOptions={{ color }}
        />
      ))}
      {currentCircle && (
        <Circle
          center={currentCircle.center}
          radius={currentCircle.radius}
          pathOptions={{ color, dashArray: "4" }} // dashed preview
        />
      )}
    </>
  );
}
