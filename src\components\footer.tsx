import { useEffect, useState } from "react";

export default function Footer() {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer); // cleanup
  }, []);

  return (
    <footer className="flex flex-col items-center justify-between border-t px-6 py-4 sm:flex-row">
      <p className="text-sm">
        © {new Date().getFullYear()} GPS Tracking System. All rights reserved.
      </p>
      <p className="mt-2 text-sm sm:mt-0">{time.toLocaleTimeString()}</p>
    </footer>
  );
}
