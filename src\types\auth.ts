export interface LoginFormData {
  email: string;
  password: string;
}

export interface LoginResponse {
  status_code: number;
  status: number;
  message: string;
  data: {
    user: User;
    token: string;
  };
}

export interface LogoutResponse {
  status_code: number;
  status: string;
  message: string;
  data: null;
}

export interface User {
  id: number;
  name: string;
  email: string;
  image: null;
  phone: string;
}
