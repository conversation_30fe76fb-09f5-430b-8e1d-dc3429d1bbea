import type {
  Area,
  CreateAreaFormData,
  CreateAreaResponse,
  DeleteAreaResponse,
  EditAreaFormData,
  EditAreaResponse,
  GetAreasResponse,
  GetOneAreaResponse,
} from "@/types/areas";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";
import { areaShapePointsAtom } from "./area-shape-points-atom";
import { selectedDrawerTypeAtom } from "./selected-drawer-type-atom";

interface AreasAtom {
  areas: Area[];
  area: Area | null;
}

interface AreasAtomAction {
  getAreas: () => void;
  createArea: (formData: CreateAreaFormData, onSuccess?: () => void) => void;
  getOneArea: (slug: string) => void;
  editArea: (
    slug: string,
    formData: EditAreaFormData,
    onSuccess?: () => void,
  ) => void;
  deleteArea: (slug: string, onSuccess?: () => void) => void;
}

export const areasAtom = atom<AreasAtom, AreasAtomAction>({
  key: "areas-atom",
  default: {
    areas: [],
    area: null,
  },

  actions: {
    async getAreas() {
      try {
        const { data } = await endpoint.get<GetAreasResponse>("areas");
        areasAtom.change("areas", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createArea(formData: CreateAreaFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.post<CreateAreaResponse>(
          "areas",
          formData,
        );

        toast.success(data.message);

        onSuccess?.();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneArea(slug: string) {
      try {
        const { data } = await endpoint.get<GetOneAreaResponse>(
          `areas/${slug}`,
        );
        areasAtom.change("area", data.data);
        selectedDrawerTypeAtom.change("selectedType", data.data.type);
        areaShapePointsAtom.change(
          "value",
          data.data.type === 1
            ? { center: data.data.center, radius: data.data.radius }
            : { ...data.data.area },
        );
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteArea(slug: string, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteAreaResponse>(
          `areas/${slug}`,
        );
        toast.success(data.data);
        onSuccess?.();
        areasAtom.getAreas();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editArea(
      slug: string,
      formData: EditAreaFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.put<EditAreaResponse>(
          `areas/${slug}`,
          formData,
        );

        toast.success(data.message);

        onSuccess?.();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
