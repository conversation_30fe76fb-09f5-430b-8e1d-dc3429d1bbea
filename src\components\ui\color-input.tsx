import { cn } from "@/lib/utils";
import * as React from "react";

interface ColorInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const ColorInput = React.forwardRef<HTMLInputElement, ColorInputProps>(
  (
    { className, value = "#000000", onChange, placeholder, disabled, ...props },
    ref,
  ) => {
    const [internalValue, setInternalValue] = React.useState(value);
    const hiddenInputRef = React.useRef<HTMLInputElement>(null);

    React.useEffect(() => {
      setInternalValue(value);
    }, [value]);

    const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setInternalValue(newValue);
      onChange?.(newValue);
    };

    const handleTriggerClick = () => {
      if (!disabled && hiddenInputRef.current) {
        hiddenInputRef.current.click();
      }
    };

    return (
      <div className="relative">
        <button
          type="button"
          onClick={handleTriggerClick}
          disabled={disabled}
          className={cn(
            "border-input placeholder:text-muted-foreground dark:bg-input/30 flex h-9 w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-1 text-sm shadow-xs transition-[color,box-shadow] outline-none",
            "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
            "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
            "disabled:cursor-not-allowed disabled:opacity-50",
            "hover:bg-accent/50",
            className,
          )}
          {...props}
        >
          <div className="flex items-center gap-2">
            <div
              className="border-border h-4 w-4 rounded border"
              style={{ backgroundColor: internalValue }}
            />
            <span className="text-sm">
              {internalValue || placeholder || "Select color"}
            </span>
          </div>
        </button>
        <input
          ref={(node) => {
            hiddenInputRef.current = node;
            if (typeof ref === "function") {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          type="color"
          value={internalValue}
          onChange={handleColorChange}
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          disabled={disabled}
        />
      </div>
    );
  },
);

ColorInput.displayName = "ColorInput";

export { ColorInput };
