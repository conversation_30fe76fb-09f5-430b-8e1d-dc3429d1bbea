@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.25 0 0);
  --foreground: oklch(0.97 0 0);
  --card: oklch(0.3 0 0);
  --card-foreground: oklch(0.97 0 0);
  --popover: oklch(0.3 0 0);
  --popover-foreground: oklch(0.97 0 0);

  --primary: oklch(0.88 0 0);
  --primary-foreground: oklch(0.25 0 0);

  --secondary: oklch(0.38 0 0);
  --secondary-foreground: oklch(0.97 0 0);

  --muted: oklch(0.38 0 0);
  --muted-foreground: oklch(0.75 0 0);

  --accent: oklch(0.38 0 0);
  --accent-foreground: oklch(0.97 0 0);

  --destructive: oklch(0.74 0.191 22.216);

  --border: oklch(1 0 0 / 15%);
  --input: oklch(1 0 0 / 20%);
  --ring: oklch(0.65 0 0);

  --chart-1: oklch(0.55 0.243 264.376);
  --chart-2: oklch(0.72 0.17 162.48);
  --chart-3: oklch(0.8 0.188 70.08);
  --chart-4: oklch(0.67 0.265 303.9);
  --chart-5: oklch(0.69 0.246 16.439);

  --sidebar: oklch(0.3 0 0);
  --sidebar-foreground: oklch(0.97 0 0);
  --sidebar-primary: oklch(0.55 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.97 0 0);
  --sidebar-accent: oklch(0.38 0 0);
  --sidebar-accent-foreground: oklch(0.97 0 0);
  --sidebar-border: oklch(1 0 0 / 15%);
  --sidebar-ring: oklch(0.65 0 0);
}

/* .dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
} */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* @import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;

  --background: oklch(0.98 0.02 240);
  --foreground: oklch(0.2 0.05 240);

  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2 0.05 240);

  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0.05 240);

  --primary: oklch(0.55 0.18 240);
  --primary-foreground: oklch(0.98 0.02 240);

  --secondary: oklch(0.85 0.05 240);
  --secondary-foreground: oklch(0.2 0.05 240);

  --muted: oklch(0.92 0.03 240);
  --muted-foreground: oklch(0.4 0.05 240);

  --accent: oklch(0.88 0.05 240);
  --accent-foreground: oklch(0.2 0.05 240);

  --destructive: oklch(0.6 0.18 25);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.9 0.02 240);
  --ring: oklch(0.55 0.18 240);

  --chart-1: oklch(0.55 0.18 240);
  --chart-2: oklch(0.62 0.16 220);
  --chart-3: oklch(0.45 0.18 250);
  --chart-4: oklch(0.7 0.12 210);
  --chart-5: oklch(0.6 0.14 260);

  --sidebar: oklch(0.98 0.02 240);
  --sidebar-foreground: oklch(0.2 0.05 240);
  --sidebar-primary: oklch(0.55 0.18 240);
  --sidebar-primary-foreground: oklch(0.98 0.02 240);
  --sidebar-accent: oklch(0.88 0.05 240);
  --sidebar-accent-foreground: oklch(0.2 0.05 240);
  --sidebar-border: oklch(0.9 0.02 240);
  --sidebar-ring: oklch(0.55 0.18 240);
}

.dark {
  --background: oklch(0.18 0.03 240);
  --foreground: oklch(0.95 0.02 240);

  --card: oklch(0.22 0.04 240);
  --card-foreground: oklch(0.95 0.02 240);

  --popover: oklch(0.22 0.04 240);
  --popover-foreground: oklch(0.95 0.02 240);

  --primary: oklch(0.7 0.14 240);
  --primary-foreground: oklch(0.15 0.03 240);

  --secondary: oklch(0.28 0.05 240);
  --secondary-foreground: oklch(0.95 0.02 240);

  --muted: oklch(0.28 0.05 240);
  --muted-foreground: oklch(0.7 0.02 240);

  --accent: oklch(0.3 0.05 240);
  --accent-foreground: oklch(0.95 0.02 240);

  --destructive: oklch(0.65 0.18 25);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.6 0.14 240);

  --chart-1: oklch(0.65 0.14 240);
  --chart-2: oklch(0.55 0.16 220);
  --chart-3: oklch(0.75 0.12 260);
  --chart-4: oklch(0.6 0.14 210);
  --chart-5: oklch(0.7 0.14 250);

  --sidebar: oklch(0.22 0.04 240);
  --sidebar-foreground: oklch(0.95 0.02 240);
  --sidebar-primary: oklch(0.65 0.14 240);
  --sidebar-primary-foreground: oklch(0.15 0.03 240);
  --sidebar-accent: oklch(0.3 0.05 240);
  --sidebar-accent-foreground: oklch(0.95 0.02 240);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.6 0.14 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
} */
