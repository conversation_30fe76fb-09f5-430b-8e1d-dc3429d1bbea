import { Button } from "@/components/ui/button";
import { Maximize } from "lucide-react";

export default function FullScreenToggle() {
  function handleFullScreen() {
    if (document.fullscreenElement) {
      document.exitFullscreen();
      return;
    }
    document.getElementById("root")!.requestFullscreen();
  }

  return (
    <Button variant="outline" size="icon" onClick={handleFullScreen}>
      <Maximize />
    </Button>
  );
}
